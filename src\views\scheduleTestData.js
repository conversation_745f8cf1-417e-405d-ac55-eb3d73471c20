export default [
  {
    ScheduleItemCode: "001||001",
    ServiceDate: "2025-08-23",
    WeekDay: "6",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "001",
    DepartmentName: "内科门诊",
    ClinicRoomCode: "101",
    ClinicRoomName: "内科诊室1",
    DoctorCode: "D001",
    Doctor<PERSON><PERSON>: "李明",
    DoctorTitleCode: "1",
    Doctor<PERSON>it<PERSON>: "主任医师",
    DoctorSpec: "心血管疾病",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S001",
    ServiceName: "心血管内科",
    Fee: "50",
    RegFee: "5",
    CheckupFee: "35",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "1,2,3,4,5,6,7,8,9,10",
    AdmitAddress: "1号楼一楼101诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "专家门诊，需提前预约",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "30",
    AvailableLeftNum: "10"
  },
  {
    ScheduleItemCode: "002||002",
    ServiceDate: "2025-08-23",
    WeekDay: "6",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "002",
    DepartmentName: "外科门诊",
    ClinicRoomCode: "201",
    ClinicRoomName: "外科诊室1",
    DoctorCode: "D002",
    DoctorName: "王强",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "普通外科",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S002",
    ServiceName: "普通外科",
    Fee: "35",
    RegFee: "5",
    CheckupFee: "25",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "11,12,13,14,15,16,17,18,19,20",
    AdmitAddress: "2号楼二楼201诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "普通门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "25",
    AvailableLeftNum: "10"
  },
  {
    ScheduleItemCode: "003||003",
    ServiceDate: "2025-08-24",
    WeekDay: "7",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "003",
    DepartmentName: "儿科门诊",
    ClinicRoomCode: "301",
    ClinicRoomName: "儿科诊室1",
    DoctorCode: "D003",
    DoctorName: "张丽",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "儿童呼吸系统疾病",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S003",
    ServiceName: "儿科",
    Fee: "45",
    RegFee: "5",
    CheckupFee: "30",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "21,22,23,24,25,26,27,28",
    AdmitAddress: "3号楼三楼301诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "儿科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "20",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "004||004",
    ServiceDate: "2025-08-24",
    WeekDay: "7",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "004",
    DepartmentName: "妇产科门诊",
    ClinicRoomCode: "401",
    ClinicRoomName: "妇产科诊室1",
    DoctorCode: "D004",
    DoctorName: "陈红",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "妇科疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S004",
    ServiceName: "妇产科",
    Fee: "30",
    RegFee: "5",
    CheckupFee: "20",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "29,30,31,32,33,34,35",
    AdmitAddress: "4号楼四楼401诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "妇科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "15",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "005||005",
    ServiceDate: "2025-08-25",
    WeekDay: "1",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "005",
    DepartmentName: "眼科门诊",
    ClinicRoomCode: "501",
    ClinicRoomName: "眼科诊室1",
    DoctorCode: "D005",
    DoctorName: "刘伟",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "眼底疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S005",
    ServiceName: "眼科",
    Fee: "40",
    RegFee: "5",
    CheckupFee: "28",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "36,37,38,39,40,41,42,43,44,45",
    AdmitAddress: "5号楼五楼501诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "眼科专科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "25",
    AvailableLeftNum: "10"
  },
  {
    ScheduleItemCode: "006||006",
    ServiceDate: "2025-08-25",
    WeekDay: "1",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "006",
    DepartmentName: "耳鼻喉科门诊",
    ClinicRoomCode: "601",
    ClinicRoomName: "耳鼻喉科诊室1",
    DoctorCode: "D006",
    DoctorName: "赵敏",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "鼻炎治疗",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S006",
    ServiceName: "耳鼻喉科",
    Fee: "28",
    RegFee: "5",
    CheckupFee: "18",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "46,47,48,49,50",
    AdmitAddress: "6号楼六楼601诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "耳鼻喉科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "20",
    AvailableLeftNum: "5"
  },
  {
    ScheduleItemCode: "007||007",
    ServiceDate: "2025-08-26",
    WeekDay: "2",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "007",
    DepartmentName: "皮肤科门诊",
    ClinicRoomCode: "701",
    ClinicRoomName: "皮肤科诊室1",
    DoctorCode: "D007",
    DoctorName: "孙华",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "皮肤病诊治",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S007",
    ServiceName: "皮肤科",
    Fee: "42",
    RegFee: "5",
    CheckupFee: "30",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "51,52,53,54,55,56,57,58",
    AdmitAddress: "7号楼七楼701诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "皮肤科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "18",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "008||008",
    ServiceDate: "2025-08-26",
    WeekDay: "2",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "008",
    DepartmentName: "神经内科门诊",
    ClinicRoomCode: "801",
    ClinicRoomName: "神经内科诊室1",
    DoctorCode: "D008",
    DoctorName: "周杰",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "神经系统疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S008",
    ServiceName: "神经内科",
    Fee: "38",
    RegFee: "5",
    CheckupFee: "26",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "59,60,61,62,63,64,65,66,67",
    AdmitAddress: "8号楼八楼801诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "神经内科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "22",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "009||009",
    ServiceDate: "2025-08-27",
    WeekDay: "3",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "009",
    DepartmentName: "骨科门诊",
    ClinicRoomCode: "901",
    ClinicRoomName: "骨科诊室1",
    DoctorCode: "D009",
    DoctorName: "吴刚",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "骨折治疗",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S009",
    ServiceName: "骨科",
    Fee: "48",
    RegFee: "5",
    CheckupFee: "35",
    ServiceFee: "7",
    OtherFee: "1",
    AvailableNumStr: "68,69,70,71,72,73,74,75,76,77",
    AdmitAddress: "9号楼九楼901诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "骨科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "28",
    AvailableLeftNum: "10"
  },
  {
    ScheduleItemCode: "010||010",
    ServiceDate: "2025-08-27",
    WeekDay: "3",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "010",
    DepartmentName: "泌尿外科门诊",
    ClinicRoomCode: "1001",
    ClinicRoomName: "泌尿外科诊室1",
    DoctorCode: "D010",
    DoctorName: "郑磊",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "泌尿系统疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S010",
    ServiceName: "泌尿外科",
    Fee: "32",
    RegFee: "5",
    CheckupFee: "22",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "78,79,80,81,82,83,84",
    AdmitAddress: "10号楼十楼1001诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "泌尿外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "16",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "011||011",
    ServiceDate: "2025-08-28",
    WeekDay: "4",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "011",
    DepartmentName: "心血管内科门诊",
    ClinicRoomCode: "1101",
    ClinicRoomName: "心血管内科诊室1",
    DoctorCode: "D011",
    DoctorName: "钱伟",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "冠心病治疗",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S011",
    ServiceName: "心血管内科",
    Fee: "55",
    RegFee: "5",
    CheckupFee: "40",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "85,86,87,88,89,90,91,92,93,94,95",
    AdmitAddress: "11号楼十一楼1101诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "心血管专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "30",
    AvailableLeftNum: "11"
  },
  {
    ScheduleItemCode: "012||012",
    ServiceDate: "2025-08-28",
    WeekDay: "4",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "012",
    DepartmentName: "消化内科门诊",
    ClinicRoomCode: "1201",
    ClinicRoomName: "消化内科诊室1",
    DoctorCode: "D012",
    DoctorName: "冯丽",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "胃肠疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S012",
    ServiceName: "消化内科",
    Fee: "36",
    RegFee: "5",
    CheckupFee: "25",
    ServiceFee: "6",
    OtherFee: "0",
    AvailableNumStr: "96,97,98,99,100,101,102,103",
    AdmitAddress: "12号楼十二楼1201诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "消化内科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "24",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "013||013",
    ServiceDate: "2025-08-29",
    WeekDay: "5",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "013",
    DepartmentName: "呼吸内科门诊",
    ClinicRoomCode: "1301",
    ClinicRoomName: "呼吸内科诊室1",
    DoctorCode: "D013",
    DoctorName: "蒋涛",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "肺部疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S013",
    ServiceName: "呼吸内科",
    Fee: "30",
    RegFee: "5",
    CheckupFee: "20",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "104,105,106,107,108,109,110",
    AdmitAddress: "13号楼十三楼1301诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "呼吸内科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "20",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "014||014",
    ServiceDate: "2025-08-29",
    WeekDay: "5",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "014",
    DepartmentName: "内分泌科门诊",
    ClinicRoomCode: "1401",
    ClinicRoomName: "内分泌科诊室1",
    DoctorCode: "D014",
    DoctorName: "韩雪",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "糖尿病治疗",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S014",
    ServiceName: "内分泌科",
    Fee: "46",
    RegFee: "5",
    CheckupFee: "32",
    ServiceFee: "7",
    OtherFee: "2",
    AvailableNumStr: "111,112,113,114,115,116,117,118,119",
    AdmitAddress: "14号楼十四楼1401诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "内分泌专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "26",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "015||015",
    ServiceDate: "2025-08-30",
    WeekDay: "6",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "015",
    DepartmentName: "肾内科门诊",
    ClinicRoomCode: "1501",
    ClinicRoomName: "肾内科诊室1",
    DoctorCode: "D015",
    DoctorName: "曹军",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "肾脏疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S015",
    ServiceName: "肾内科",
    Fee: "39",
    RegFee: "5",
    CheckupFee: "27",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "120,121,122,123,124,125,126,127,128",
    AdmitAddress: "15号楼十五楼1501诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "肾内科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "23",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "016||016",
    ServiceDate: "2025-08-30",
    WeekDay: "6",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "016",
    DepartmentName: "血液科门诊",
    ClinicRoomCode: "1601",
    ClinicRoomName: "血液科诊室1",
    DoctorCode: "D016",
    DoctorName: "谢敏",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "血液病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S016",
    ServiceName: "血液科",
    Fee: "33",
    RegFee: "5",
    CheckupFee: "23",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "129,130,131,132,133,134",
    AdmitAddress: "16号楼十六楼1601诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "血液科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "18",
    AvailableLeftNum: "6"
  },
  {
    ScheduleItemCode: "017||017",
    ServiceDate: "2025-08-31",
    WeekDay: "7",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "017",
    DepartmentName: "肿瘤科门诊",
    ClinicRoomCode: "1701",
    ClinicRoomName: "肿瘤科诊室1",
    DoctorCode: "D017",
    DoctorName: "袁强",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "肿瘤治疗",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S017",
    ServiceName: "肿瘤科",
    Fee: "60",
    RegFee: "5",
    CheckupFee: "45",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "135,136,137,138,139,140,141,142",
    AdmitAddress: "17号楼十七楼1701诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "肿瘤专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "25",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "018||018",
    ServiceDate: "2025-08-31",
    WeekDay: "7",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "018",
    DepartmentName: "风湿免疫科门诊",
    ClinicRoomCode: "1801",
    ClinicRoomName: "风湿免疫科诊室1",
    DoctorCode: "D018",
    DoctorName: "顾丽",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "风湿性疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S018",
    ServiceName: "风湿免疫科",
    Fee: "41",
    RegFee: "5",
    CheckupFee: "29",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "143,144,145,146,147,148,149",
    AdmitAddress: "18号楼十八楼1801诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "风湿免疫科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "21",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "019||019",
    ServiceDate: "2025-09-01",
    WeekDay: "1",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "019",
    DepartmentName: "精神科门诊",
    ClinicRoomCode: "1901",
    ClinicRoomName: "精神科诊室1",
    DoctorCode: "D019",
    DoctorName: "邓伟",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "心理疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S019",
    ServiceName: "精神科",
    Fee: "35",
    RegFee: "5",
    CheckupFee: "25",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "150,151,152,153,154,155",
    AdmitAddress: "19号楼十九楼1901诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "精神科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "15",
    AvailableLeftNum: "6"
  },
  {
    ScheduleItemCode: "020||020",
    ServiceDate: "2025-09-01",
    WeekDay: "1",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "020",
    DepartmentName: "康复科门诊",
    ClinicRoomCode: "2001",
    ClinicRoomName: "康复科诊室1",
    DoctorCode: "D020",
    DoctorName: "薛华",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "康复治疗",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S020",
    ServiceName: "康复科",
    Fee: "37",
    RegFee: "5",
    CheckupFee: "26",
    ServiceFee: "6",
    OtherFee: "0",
    AvailableNumStr: "156,157,158,159,160,161,162,163",
    AdmitAddress: "20号楼二十楼2001诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "康复科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "19",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "021||021",
    ServiceDate: "2025-09-02",
    WeekDay: "2",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "021",
    DepartmentName: "急诊科门诊",
    ClinicRoomCode: "2101",
    ClinicRoomName: "急诊科诊室1",
    DoctorCode: "D021",
    DoctorName: "高峰",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "急诊医学",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S021",
    ServiceName: "急诊科",
    Fee: "25",
    RegFee: "5",
    CheckupFee: "15",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "164,165,166,167,168,169,170,171,172,173",
    AdmitAddress: "急诊楼一楼2101诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "急诊科门诊",
    RecordCount: "50",
    TimeRangeFlag: "0",
    ScheduleStatus: "N",
    AvailableTotalNum: "50",
    AvailableLeftNum: "10"
  },
  {
    ScheduleItemCode: "022||022",
    ServiceDate: "2025-09-02",
    WeekDay: "2",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "022",
    DepartmentName: "中医科门诊",
    ClinicRoomCode: "2201",
    ClinicRoomName: "中医科诊室1",
    DoctorCode: "D022",
    DoctorName: "林静",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "中医内科",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S022",
    ServiceName: "中医科",
    Fee: "52",
    RegFee: "5",
    CheckupFee: "38",
    ServiceFee: "7",
    OtherFee: "2",
    AvailableNumStr: "174,175,176,177,178,179,180,181,182",
    AdmitAddress: "中医楼二楼2201诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "中医专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "27",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "023||023",
    ServiceDate: "2025-09-03",
    WeekDay: "3",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "023",
    DepartmentName: "针灸科门诊",
    ClinicRoomCode: "2301",
    ClinicRoomName: "针灸科诊室1",
    DoctorCode: "D023",
    DoctorName: "何军",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "针灸推拿",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S023",
    ServiceName: "针灸科",
    Fee: "43",
    RegFee: "5",
    CheckupFee: "30",
    ServiceFee: "7",
    OtherFee: "1",
    AvailableNumStr: "183,184,185,186,187,188,189,190",
    AdmitAddress: "中医楼三楼2301诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "针灸科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "22",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "024||024",
    ServiceDate: "2025-09-03",
    WeekDay: "3",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "024",
    DepartmentName: "口腔科门诊",
    ClinicRoomCode: "2401",
    ClinicRoomName: "口腔科诊室1",
    DoctorCode: "D024",
    DoctorName: "罗敏",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "口腔疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S024",
    ServiceName: "口腔科",
    Fee: "34",
    RegFee: "5",
    CheckupFee: "24",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "191,192,193,194,195,196,197",
    AdmitAddress: "口腔楼一楼2401诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "口腔科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "17",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "025||025",
    ServiceDate: "2025-09-04",
    WeekDay: "4",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "025",
    DepartmentName: "麻醉科门诊",
    ClinicRoomCode: "2501",
    ClinicRoomName: "麻醉科诊室1",
    DoctorCode: "D025",
    DoctorName: "田华",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "麻醉学",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S025",
    ServiceName: "麻醉科",
    Fee: "40",
    RegFee: "5",
    CheckupFee: "28",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "198,199,200,201,202,203,204,205,206",
    AdmitAddress: "手术楼二楼2501诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "麻醉科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "24",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "026||026",
    ServiceDate: "2025-09-04",
    WeekDay: "4",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "026",
    DepartmentName: "病理科门诊",
    ClinicRoomCode: "2601",
    ClinicRoomName: "病理科诊室1",
    DoctorCode: "D026",
    DoctorName: "胡强",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "病理诊断",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S026",
    ServiceName: "病理科",
    Fee: "58",
    RegFee: "5",
    CheckupFee: "43",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "207,208,209,210,211,212",
    AdmitAddress: "检验楼三楼2601诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "病理专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "15",
    AvailableLeftNum: "6"
  },
  {
    ScheduleItemCode: "027||027",
    ServiceDate: "2025-09-05",
    WeekDay: "5",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "027",
    DepartmentName: "检验科门诊",
    ClinicRoomCode: "2701",
    ClinicRoomName: "检验科诊室1",
    DoctorCode: "D027",
    DoctorName: "梁丽",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "临床检验",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S027",
    ServiceName: "检验科",
    Fee: "28",
    RegFee: "5",
    CheckupFee: "18",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "213,214,215,216,217,218,219,220",
    AdmitAddress: "检验楼一楼2701诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "检验科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "20",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "028||028",
    ServiceDate: "2025-09-05",
    WeekDay: "5",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "028",
    DepartmentName: "影像科门诊",
    ClinicRoomCode: "2801",
    ClinicRoomName: "影像科诊室1",
    DoctorCode: "D028",
    DoctorName: "彭伟",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "医学影像",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S028",
    ServiceName: "影像科",
    Fee: "44",
    RegFee: "5",
    CheckupFee: "31",
    ServiceFee: "7",
    OtherFee: "1",
    AvailableNumStr: "221,222,223,224,225,226,227,228,229",
    AdmitAddress: "影像楼二楼2801诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "影像科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "26",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "029||029",
    ServiceDate: "2025-09-06",
    WeekDay: "6",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "029",
    DepartmentName: "超声科门诊",
    ClinicRoomCode: "2901",
    ClinicRoomName: "超声科诊室1",
    DoctorCode: "D029",
    DoctorName: "杨静",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "超声诊断",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S029",
    ServiceName: "超声科",
    Fee: "31",
    RegFee: "5",
    CheckupFee: "21",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "230,231,232,233,234,235,236,237",
    AdmitAddress: "影像楼三楼2901诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "超声科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "18",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "030||030",
    ServiceDate: "2025-09-06",
    WeekDay: "6",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "030",
    DepartmentName: "心电图科门诊",
    ClinicRoomCode: "3001",
    ClinicRoomName: "心电图科诊室1",
    DoctorCode: "D030",
    DoctorName: "陆军",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "心电图诊断",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S030",
    ServiceName: "心电图科",
    Fee: "26",
    RegFee: "5",
    CheckupFee: "16",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "238,239,240,241,242,243,244",
    AdmitAddress: "功能检查楼一楼3001诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "心电图科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "16",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "031||031",
    ServiceDate: "2025-09-07",
    WeekDay: "7",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "031",
    DepartmentName: "胸外科门诊",
    ClinicRoomCode: "3101",
    ClinicRoomName: "胸外科诊室1",
    DoctorCode: "D031",
    DoctorName: "马强",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "胸部疾病",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S031",
    ServiceName: "胸外科",
    Fee: "53",
    RegFee: "5",
    CheckupFee: "38",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "245,246,247,248,249,250,251,252,253",
    AdmitAddress: "外科楼四楼3101诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "胸外科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "25",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "032||032",
    ServiceDate: "2025-09-07",
    WeekDay: "7",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "032",
    DepartmentName: "心外科门诊",
    ClinicRoomCode: "3201",
    ClinicRoomName: "心外科诊室1",
    DoctorCode: "D032",
    DoctorName: "丁丽",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "心脏疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S032",
    ServiceName: "心外科",
    Fee: "47",
    RegFee: "5",
    CheckupFee: "34",
    ServiceFee: "7",
    OtherFee: "1",
    AvailableNumStr: "254,255,256,257,258,259,260,261",
    AdmitAddress: "外科楼五楼3201诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "心外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "23",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "033||033",
    ServiceDate: "2025-09-08",
    WeekDay: "1",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "033",
    DepartmentName: "神经外科门诊",
    ClinicRoomCode: "3301",
    ClinicRoomName: "神经外科诊室1",
    DoctorCode: "D033",
    DoctorName: "石华",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "脑部疾病",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S033",
    ServiceName: "神经外科",
    Fee: "62",
    RegFee: "5",
    CheckupFee: "47",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "262,263,264,265,266,267,268",
    AdmitAddress: "外科楼六楼3301诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "神经外科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "20",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "034||034",
    ServiceDate: "2025-09-08",
    WeekDay: "1",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "034",
    DepartmentName: "整形外科门诊",
    ClinicRoomCode: "3401",
    ClinicRoomName: "整形外科诊室1",
    DoctorCode: "D034",
    DoctorName: "江敏",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "整形美容",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S034",
    ServiceName: "整形外科",
    Fee: "38",
    RegFee: "5",
    CheckupFee: "28",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "269,270,271,272,273,274,275,276",
    AdmitAddress: "美容楼二楼3401诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "整形外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "21",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "035||035",
    ServiceDate: "2025-09-09",
    WeekDay: "2",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "035",
    DepartmentName: "烧伤科门诊",
    ClinicRoomCode: "3501",
    ClinicRoomName: "烧伤科诊室1",
    DoctorCode: "D035",
    DoctorName: "范伟",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "烧伤治疗",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S035",
    ServiceName: "烧伤科",
    Fee: "45",
    RegFee: "5",
    CheckupFee: "32",
    ServiceFee: "7",
    OtherFee: "1",
    AvailableNumStr: "277,278,279,280,281,282,283,284,285",
    AdmitAddress: "外科楼七楼3501诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "烧伤科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "24",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "036||036",
    ServiceDate: "2025-09-09",
    WeekDay: "2",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "036",
    DepartmentName: "血管外科门诊",
    ClinicRoomCode: "3601",
    ClinicRoomName: "血管外科诊室1",
    DoctorCode: "D036",
    DoctorName: "秦军",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "血管疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S036",
    ServiceName: "血管外科",
    Fee: "36",
    RegFee: "5",
    CheckupFee: "26",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "286,287,288,289,290,291,292",
    AdmitAddress: "外科楼八楼3601诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "血管外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "19",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "037||037",
    ServiceDate: "2025-09-10",
    WeekDay: "3",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "037",
    DepartmentName: "肝胆外科门诊",
    ClinicRoomCode: "3701",
    ClinicRoomName: "肝胆外科诊室1",
    DoctorCode: "D037",
    DoctorName: "尹华",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "肝胆疾病",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S037",
    ServiceName: "肝胆外科",
    Fee: "56",
    RegFee: "5",
    CheckupFee: "41",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "293,294,295,296,297,298,299,300,301",
    AdmitAddress: "外科楼九楼3701诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "肝胆外科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "27",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "038||038",
    ServiceDate: "2025-09-10",
    WeekDay: "3",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "038",
    DepartmentName: "乳腺外科门诊",
    ClinicRoomCode: "3801",
    ClinicRoomName: "乳腺外科诊室1",
    DoctorCode: "D038",
    DoctorName: "许丽",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "乳腺疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S038",
    ServiceName: "乳腺外科",
    Fee: "42",
    RegFee: "5",
    CheckupFee: "30",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "302,303,304,305,306,307,308,309",
    AdmitAddress: "外科楼十楼3801诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "乳腺外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "22",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "039||039",
    ServiceDate: "2025-09-11",
    WeekDay: "4",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "039",
    DepartmentName: "甲状腺外科门诊",
    ClinicRoomCode: "3901",
    ClinicRoomName: "甲状腺外科诊室1",
    DoctorCode: "D039",
    DoctorName: "卢强",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "甲状腺疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S039",
    ServiceName: "甲状腺外科",
    Fee: "34",
    RegFee: "5",
    CheckupFee: "24",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "310,311,312,313,314,315,316,317,318",
    AdmitAddress: "外科楼十一楼3901诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "甲状腺外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "25",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "040||040",
    ServiceDate: "2025-09-11",
    WeekDay: "4",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "040",
    DepartmentName: "手外科门诊",
    ClinicRoomCode: "4001",
    ClinicRoomName: "手外科诊室1",
    DoctorCode: "D040",
    DoctorName: "沈敏",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "手部疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S040",
    ServiceName: "手外科",
    Fee: "39",
    RegFee: "5",
    CheckupFee: "27",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "319,320,321,322,323,324,325,326",
    AdmitAddress: "外科楼十二楼4001诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "手外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "20",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "041||041",
    ServiceDate: "2025-09-12",
    WeekDay: "5",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "041",
    DepartmentName: "脊柱外科门诊",
    ClinicRoomCode: "4101",
    ClinicRoomName: "脊柱外科诊室1",
    DoctorCode: "D041",
    DoctorName: "汤华",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "脊柱疾病",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S041",
    ServiceName: "脊柱外科",
    Fee: "58",
    RegFee: "5",
    CheckupFee: "43",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "327,328,329,330,331,332,333,334",
    AdmitAddress: "骨科楼一楼4101诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "脊柱外科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "26",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "042||042",
    ServiceDate: "2025-09-12",
    WeekDay: "5",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "042",
    DepartmentName: "关节外科门诊",
    ClinicRoomCode: "4201",
    ClinicRoomName: "关节外科诊室1",
    DoctorCode: "D042",
    DoctorName: "姚丽",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "关节疾病",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S042",
    ServiceName: "关节外科",
    Fee: "44",
    RegFee: "5",
    CheckupFee: "31",
    ServiceFee: "7",
    OtherFee: "1",
    AvailableNumStr: "335,336,337,338,339,340,341,342,343",
    AdmitAddress: "骨科楼二楼4201诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "关节外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "24",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "043||043",
    ServiceDate: "2025-09-13",
    WeekDay: "6",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "043",
    DepartmentName: "创伤外科门诊",
    ClinicRoomCode: "4301",
    ClinicRoomName: "创伤外科诊室1",
    DoctorCode: "D043",
    DoctorName: "魏强",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "创伤治疗",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S043",
    ServiceName: "创伤外科",
    Fee: "32",
    RegFee: "5",
    CheckupFee: "22",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "344,345,346,347,348,349,350",
    AdmitAddress: "骨科楼三楼4301诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "创伤外科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "18",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "044||044",
    ServiceDate: "2025-09-13",
    WeekDay: "6",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "044",
    DepartmentName: "运动医学科门诊",
    ClinicRoomCode: "4401",
    ClinicRoomName: "运动医学科诊室1",
    DoctorCode: "D044",
    DoctorName: "贾敏",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "运动损伤",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S044",
    ServiceName: "运动医学科",
    Fee: "41",
    RegFee: "5",
    CheckupFee: "29",
    ServiceFee: "6",
    OtherFee: "1",
    AvailableNumStr: "351,352,353,354,355,356,357,358",
    AdmitAddress: "骨科楼四楼4401诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "运动医学科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "21",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "045||045",
    ServiceDate: "2025-09-14",
    WeekDay: "7",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "045",
    DepartmentName: "疼痛科门诊",
    ClinicRoomCode: "4501",
    ClinicRoomName: "疼痛科诊室1",
    DoctorCode: "D045",
    DoctorName: "龚华",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "疼痛治疗",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S045",
    ServiceName: "疼痛科",
    Fee: "49",
    RegFee: "5",
    CheckupFee: "35",
    ServiceFee: "7",
    OtherFee: "2",
    AvailableNumStr: "359,360,361,362,363,364,365,366,367",
    AdmitAddress: "疼痛楼一楼4501诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "疼痛科专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "28",
    AvailableLeftNum: "9"
  },
  {
    ScheduleItemCode: "046||046",
    ServiceDate: "2025-09-14",
    WeekDay: "7",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "046",
    DepartmentName: "老年医学科门诊",
    ClinicRoomCode: "4601",
    ClinicRoomName: "老年医学科诊室1",
    DoctorCode: "D046",
    DoctorName: "常丽",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "老年疾病",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S046",
    ServiceName: "老年医学科",
    Fee: "33",
    RegFee: "5",
    CheckupFee: "23",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "368,369,370,371,372,373,374,375",
    AdmitAddress: "老年楼一楼4601诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "老年医学科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "19",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "047||047",
    ServiceDate: "2025-09-15",
    WeekDay: "1",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "047",
    DepartmentName: "全科医学科门诊",
    ClinicRoomCode: "4701",
    ClinicRoomName: "全科医学科诊室1",
    DoctorCode: "D047",
    DoctorName: "毛强",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任医师",
    DoctorSpec: "全科医学",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任医师门诊",
    ServiceCode: "S047",
    ServiceName: "全科医学科",
    Fee: "27",
    RegFee: "5",
    CheckupFee: "17",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "376,377,378,379,380,381,382,383,384,385",
    AdmitAddress: "全科楼一楼4701诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "全科医学科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "30",
    AvailableLeftNum: "10"
  },
  {
    ScheduleItemCode: "048||048",
    ServiceDate: "2025-09-15",
    WeekDay: "1",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "048",
    DepartmentName: "营养科门诊",
    ClinicRoomCode: "4801",
    ClinicRoomName: "营养科诊室1",
    DoctorCode: "D048",
    DoctorName: "白敏",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "营养治疗",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S048",
    ServiceName: "营养科",
    Fee: "29",
    RegFee: "5",
    CheckupFee: "19",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "386,387,388,389,390,391,392,393",
    AdmitAddress: "营养楼一楼4801诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "营养科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "22",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "049||049",
    ServiceDate: "2025-09-16",
    WeekDay: "2",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "049",
    DepartmentName: "药学门诊",
    ClinicRoomCode: "4901",
    ClinicRoomName: "药学门诊室1",
    DoctorCode: "D049",
    DoctorName: "孔华",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任药师",
    DoctorSpec: "药物咨询",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任药师门诊",
    ServiceCode: "S049",
    ServiceName: "药学门诊",
    Fee: "24",
    RegFee: "5",
    CheckupFee: "14",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "394,395,396,397,398,399,400",
    AdmitAddress: "药学楼一楼4901诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "药学门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "16",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "050||050",
    ServiceDate: "2025-09-16",
    WeekDay: "2",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "050",
    DepartmentName: "健康管理中心",
    ClinicRoomCode: "5001",
    ClinicRoomName: "健康管理中心1",
    DoctorCode: "D050",
    DoctorName: "严丽",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "健康管理",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S050",
    ServiceName: "健康管理",
    Fee: "65",
    RegFee: "5",
    CheckupFee: "50",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "401,402,403,404,405,406,407,408,409,410",
    AdmitAddress: "体检楼一楼5001诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "健康管理专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "35",
    AvailableLeftNum: "10"
  }

  ,
    {
    ScheduleItemCode: "048||048",
    ServiceDate: "2025-09-15",
    WeekDay: "1",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "048",
    DepartmentName: "营养科门诊",
    ClinicRoomCode: "4801",
    ClinicRoomName: "营养科诊室1",
    DoctorCode: "D048",
    DoctorName: "白敏",
    DoctorTitleCode: "3",
    DoctorTitle: "主治医师",
    DoctorSpec: "营养治疗",
    DoctorSessTypeCode: "3",
    DoctorSessType: "主治医师门诊",
    ServiceCode: "S048",
    ServiceName: "营养科",
    Fee: "29",
    RegFee: "5",
    CheckupFee: "19",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "386,387,388,389,390,391,392,393",
    AdmitAddress: "营养楼一楼4801诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "营养科门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "22",
    AvailableLeftNum: "8"
  },
  {
    ScheduleItemCode: "049||049",
    ServiceDate: "2025-09-16",
    WeekDay: "2",
    SessionCode: "01",
    SessionName: "上午",
    StartTime: "08:00",
    EndTime: "12:00",
    DepartmentCode: "049",
    DepartmentName: "药学门诊",
    ClinicRoomCode: "4901",
    ClinicRoomName: "药学门诊室1",
    DoctorCode: "D049",
    DoctorName: "孔华",
    DoctorTitleCode: "2",
    DoctorTitle: "副主任药师",
    DoctorSpec: "药物咨询",
    DoctorSessTypeCode: "2",
    DoctorSessType: "副主任药师门诊",
    ServiceCode: "S049",
    ServiceName: "药学门诊",
    Fee: "24",
    RegFee: "5",
    CheckupFee: "14",
    ServiceFee: "5",
    OtherFee: "0",
    AvailableNumStr: "394,395,396,397,398,399,400",
    AdmitAddress: "药学楼一楼4901诊室",
    AdmitTimeRange: "08:00-12:00",
    Note: "药学门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "16",
    AvailableLeftNum: "7"
  },
  {
    ScheduleItemCode: "050||050",
    ServiceDate: "2025-09-16",
    WeekDay: "2",
    SessionCode: "02",
    SessionName: "下午",
    StartTime: "14:00",
    EndTime: "18:00",
    DepartmentCode: "050",
    DepartmentName: "健康管理中心",
    ClinicRoomCode: "5001",
    ClinicRoomName: "健康管理中心1",
    DoctorCode: "D050",
    DoctorName: "严丽",
    DoctorTitleCode: "1",
    DoctorTitle: "主任医师",
    DoctorSpec: "健康管理",
    DoctorSessTypeCode: "1",
    DoctorSessType: "专家门诊",
    ServiceCode: "S050",
    ServiceName: "健康管理",
    Fee: "65",
    RegFee: "5",
    CheckupFee: "50",
    ServiceFee: "8",
    OtherFee: "2",
    AvailableNumStr: "401,402,403,404,405,406,407,408,409,410",
    AdmitAddress: "体检楼一楼5001诊室",
    AdmitTimeRange: "14:00-18:00",
    Note: "健康管理专家门诊",
    RecordCount: "50",
    TimeRangeFlag: "1",
    ScheduleStatus: "N",
    AvailableTotalNum: "35",
    AvailableLeftNum: "10"
  }
];
