# 专家出诊屏功能说明

## 功能概述
在 `src/views/specialistScreen.vue` 文件中创建了一个原生HTML表格，用于显示医院专家出诊排班数据。

## 表格结构
- **总列数**: 15列（5个人 × 3列/人）
- **总行数**: 10行数据行 + 1行表头
- **数据容量**: 最多同时显示50个人的信息（5人/行 × 10行）

## 列结构设计
每个人占用3个连续的列：
1. **第1列**: 科室名称 (DepartmentName)
2. **第2列**: 医生姓名 (DoctorName)  
3. **第3列**: 诊室地址 (AdmitAddress)

这个模式在每行重复5次，形成15列的表格结构。

## 数据源
- 数据来源：`scheduleTestData.js` 文件
- 数据过滤：只显示 `ScheduleStatus === "N"` 的记录
- 当前数据量：50条记录

## 核心功能

### 1. 数据显示
- 使用 `getPersonData(rowIndex, personIndex, field)` 方法获取指定位置的数据
- 支持动态计算表格位置对应的数据索引
- 自动处理数据边界情况

### 2. 循环轮播功能
- **触发条件**: 当数据超过50条时自动启动
- **轮播间隔**: 每3秒切换一次
- **轮播方式**: 通过 `currentOffset` 偏移量实现数据循环显示
- **生命周期管理**: 组件销毁时自动清理定时器

### 3. 响应式设计
- 支持不同屏幕尺寸的自适应显示
- 移动端优化的字体大小和间距
- 横向滚动支持，确保在小屏幕上也能完整显示

## 样式特色

### 表头设计
- 蓝色渐变背景 (#4a90e2 → #6bb0ff)
- 粘性定位，滚动时保持可见
- 不同列类型使用不同的蓝色深度

### 数据行设计
- **科室列**: 橙色背景 (#fff3e0)，突出显示科室信息
- **医生列**: 绿色背景 (#e8f5e8)，突出显示医生姓名
- **地址列**: 紫色背景 (#f3e5f5)，显示诊室位置
- 交替行背景色，提高可读性
- 鼠标悬停效果

### 响应式断点
- **1200px以下**: 调整字体大小和单元格宽度
- **768px以下**: 进一步压缩显示，适配移动设备

## 技术实现

### Vue 2 兼容性
- 使用Vue 2语法和生命周期钩子
- 正确处理template标签的key属性
- 使用 `beforeDestroy` 而非 `beforeUnmount`

### 性能优化
- 计算属性缓存数据处理结果
- 避免不必要的DOM重渲染
- 合理的定时器管理

## 使用方法
1. 启动开发服务器：`npm run serve`
2. 访问页面：`http://localhost:8081/specialistScreen`
3. 表格将自动显示当前的排班数据
4. 如果数据超过50条，将自动开始轮播显示

## 扩展性
- 可以通过修改轮播间隔时间来调整显示速度
- 可以调整表格行列数来适应不同的显示需求
- 支持添加更多的数据字段显示
- 样式可以根据医院品牌要求进行定制
