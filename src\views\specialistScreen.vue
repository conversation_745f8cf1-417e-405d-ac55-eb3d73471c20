<!-- 专家出诊屏 -->
<template>
  <div class="page-container">
    <h1>专家出诊屏</h1>
  </div>
</template>

<script>
import { dataConversion } from "@/utils/index.js";
import scheduleTestData from "./scheduleTestData.js";

export default {
  name: "specialistScreen",
  data() {
    return {
      // 科室列表
      departmentList: [],
      // 排班数据
      scheduleData: [],
    };
  },
  created() {
    this.getDepartmentList();
    this.getScheduleData();
  },
  methods: {
    // 获取科室列表
    async getDepartmentList() {
      const res = await this.$api.department.queryDepartment();
      console.log("二级科室列表", res);
      if (res.success) {
        console.log("请求成功");
        const department = res.data.Response?.Departments?.Department || [];
        this.departmentList = dataConversion(department);
      } else {
        this.$message.error(res.message);
      }
    },
    // 获取排班数据
    async getScheduleData() {
      this.scheduleData = scheduleTestData.filter(t => {
        return t.ScheduleStatus === "N";
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
}
</style>
